<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        nav {
            display: flex;
            flex-direction: row;
            font-family: Istok Web, sans-serif;
            justify-content: space-between;
            align-items: center;
            background-color: #000000;
            width: 100%;
            padding: 20px 100px;
        }
        ul {
            list-style: none;
            display: flex;
            flex-direction: row;
            gap: 50px;
        }
        li a {
            color: #838383;
            text-decoration: none;
        }
        li .navLinkActive {
            color: #e6b120;
        }
        .searchBar {
            display: flex;
            align-items: center;
            border-radius: 5px;
            width: 200px;
        }
        .searchBar input {
            border: none;
            border-radius: 5px 0 0 5px;
            outline: none;
            background: #323232;
            color: #ffffff;
            padding: 10px 20px;
        }
        .searchBar i {
            color: #000000;
            background: #ffcd29;
            padding: 10px 10px;
            border-radius: 0 5px 5px 0;
        }
        .tabletNav {
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }
        .tabletNav .searchBar {
            width: 100%;
            display: flex;
            justify-content: flex-end;
        }
        .tabletNav .searchBar input{
            width: 400px;
        }

        .mobileNav {
            gap: 40px;
        }
        .mobileNav .searchBar {
            width: 100%;
        }
        .mobileNav .searchBar input {
            width: 100%;
        }

        /* Parent container */
        .tabletNavLinks {
            background-color: #ffffff;
            font-family: Istok Web, sans-serif;
            padding: 20px 100px;
        }

        .tabletNavLinks ul {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
            width: 100%;
        }

        /* Center nav icon & text */
        .navIcons {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .bottomBarNavLinks {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            gap: 6px;
            color: #999999;
            text-decoration: none;
        }

        .bottomBarNavLinks:hover {
            color: #e6b120;
        }

        /* Active link styling */
        .bottomBarNavLinks.bottomBarLinksActive {
            color: #e6b120;
        }

        .mobileNavLinks {
            background-color: #ffffff;
            font-family: Istok Web, sans-serif;
            padding: 20px 100px;
        }

        .mobileNavLinks ul {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
            width: 100%;
        }

        .mobileNavLinks p {
            font-size: 10px;
        }

        footer {
            background-color: #000000;
            color: #ffffff;
            font-family: Poppins, sans-serif;
            padding: 50px 200px;
            display: flex;
            flex-direction: column;
        }
        footer h1 {
            font-size: 20px;;
        }
        .footerTop {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: top;
            margin-bottom: 20px;
        }
        .footerInfo {
            display: flex;
            justify-content: flex-start;
            flex-direction: column;
            gap: 20px;
            max-width: 300px;
        }
        .footerInfo img {
            width: 80px;
        }
        
        .footerLinks {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .footerLinks .quickLinks ul{
            display: flex;
            flex-direction: column;
            padding: 10px 0 0 20px;
            gap: 10px;
            font-size: 12px;
        }
        .footerLinks .quickLinks a:hover {
            color: #e6b120;
        }
        .footerLinks .topKategori ul{
            display: flex;
            flex-direction: column;
            padding: 10px 0 0 20px;
            gap: 10px;
            font-size: 12px;
        }
        .footerLinks .topKategori a:hover {
            color: #e6b120;
        }

        .footerSocial {
            display: flex;
            flex-direction: column;
            gap: 40px;
        }

        /* Align items inside socialLinks */
        .footerSocial .socialLinks ul li {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .footerSocial .socialLinks ul li a {
            display: flex;
            align-items: center;
            gap: 10px;
            color: inherit;
            text-decoration: none;
        }

        .footerSocial .socialLinks ul li a p {
            margin: 0;
            font-size: 14px;
        }

        /* Align address image and text horizontally */
        .footerSocial .companyAddress .addressMaps {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .footerSocial .companyAddress p {
            margin: 0;
            font-size: 14px;
        }

        .footerBottom {
            padding: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Desktop Navigation -->
    <nav class="desktopNav">
        <div class="logo">
            <a href="">
                <img src="/public/assets/logoCatalog.png" alt="Logo Catalog" width="40px">
            </a>
        </div>
        <div class="navLinks">
            <ul>
                <li><a class="navLinkActive" href="">Home</a></li>
                <li><a class="" href="">Katalog</a></li>
                <li><a class="" href="">Brands</a></li>
                <li><a class="" href="">Wishlist</a></li>
            </ul>
        </div>
        <div class="searchBar">
            <input type="text" placeholder="Cari product...">
            <a href="#">
                <i class="fa fa-search"></i>
            </a>
        </div>
    </nav>

    <!-- Tablet Navigation -->
    <nav class="tabletNav">
        <div class="logo">
            <a href="">
                <img src="/public/assets/logoCatalog.png" alt="Logo Catalog" width="40px">
            </a>
        </div>
        <div class="searchBar">
            <input type="text" placeholder="Cari product...">
            <a href="#">
                <i class="fa fa-search"></i>
            </a>
        </div>
    </nav>

    <!-- Tablet Bottom Bar NavigationLinks -->
     <div class="tabletNavLinks">
        <ul>
            <!-- navLinks with Icons -->
            <li>
                <div class="navIcons">
                    <a class="bottomBarNavLinks bottomBarLinksActive" href="">
                    <svg
                        width="30"
                        height="34"
                        viewBox="0 0 30 34"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        class="text-blue-600"
                    >
                        <path
                        d="M2.26193 31.4446H9.99755V19.1162H19.6692V31.4446H27.4048V12.5874L14.8334 3.07925L2.26193 12.5874V31.4446ZM0.166687 33.5398V11.5398L14.8334 0.460205L29.5 11.5398V33.5398H17.5739V21.2114H12.0928V33.5398H0.166687Z"
                        fill="currentColor"
                        />
                    </svg>
                    <p>Home</p>
                    </a>
                </div>
                </li>
            <li>
                <div class="navIcons">
                    <a class="bottomBarNavLinks" href="">
                        <svg
                            width="32"
                            height="32"
                            viewBox="0 0 32 32"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M0 4.80001C0 3.12001 0 2.28001 0.326 1.64001C0.614 1.07601 1.072 0.616014 1.638 0.328014C2.28 0.00201416 3.12 0.00201416 4.798 0.00201416H9.198C10.878 0.00201416 11.718 0.00201416 12.358 0.328014C12.922 0.616014 13.382 1.07401 13.67 1.64001C13.996 2.28201 13.996 3.12201 13.996 4.80001V9.20001C13.996 10.88 13.996 11.72 13.67 12.36C13.3823 12.925 12.923 13.3843 12.358 13.672C11.716 13.998 10.876 13.998 9.198 13.998H4.798C3.118 13.998 2.278 13.998 1.638 13.672C1.07301 13.3843 0.613706 12.925 0.326 12.36C0 11.718 0 10.878 0 9.20001V4.80001ZM4.8 2.00001H9.2C10.074 2.00001 10.608 2.00001 11.008 2.03401C11.1537 2.04451 11.2982 2.06726 11.44 2.10201L11.454 2.10801C11.6471 2.20657 11.8029 2.3652 11.898 2.56001L11.916 2.62401C11.932 2.69201 11.95 2.80401 11.966 2.99201C11.998 3.39201 12 3.92601 12 4.80001V9.20001C12 10.074 12 10.608 11.966 11.008C11.9555 11.1537 11.9328 11.2982 11.898 11.44L11.892 11.452V11.454C11.7957 11.6419 11.6424 11.7946 11.454 11.89C11.454 11.89 11.446 11.894 11.44 11.898L11.376 11.916C11.2551 11.9441 11.132 11.9608 11.008 11.966C10.608 11.998 10.074 12 9.2 12H4.8C3.926 12 3.392 12 2.992 11.966C2.84635 11.9555 2.70183 11.9328 2.56 11.898L2.546 11.89C2.35837 11.7942 2.20578 11.6416 2.11 11.454L2.1 11.44L2.082 11.376C2.05396 11.2552 2.03722 11.132 2.032 11.008C2.002 10.608 2 10.074 2 9.20001V4.80001C2 3.92601 2 3.39201 2.034 2.99201C2.04449 2.84636 2.06724 2.70185 2.102 2.56001L2.11 2.54401C2.20578 2.35638 2.35837 2.20379 2.546 2.10801L2.56 2.10201L2.624 2.08401C2.74485 2.05597 2.86805 2.03923 2.992 2.03401C3.392 2.00201 3.926 2.00001 4.8 2.00001ZM18 4.80001C18 3.12001 18 2.28001 18.326 1.64001C18.614 1.07601 19.072 0.616014 19.638 0.328014C20.28 0.00201416 21.12 0.00201416 22.798 0.00201416H27.198C28.878 0.00201416 29.718 0.00201416 30.358 0.328014C30.922 0.616014 31.382 1.07401 31.67 1.64001C31.998 2.28201 31.998 3.12201 31.998 4.80001V9.20001C31.998 10.88 31.998 11.72 31.67 12.36C31.3823 12.925 30.923 13.3843 30.358 13.672C29.716 13.998 28.876 13.998 27.198 13.998H22.798C21.118 13.998 20.278 13.998 19.638 13.672C19.073 13.3843 18.6137 12.925 18.326 12.36C18 11.718 18 10.878 18 9.20001V4.80001ZM22.8 2.00001H27.2C28.072 2.00001 28.608 2.00001 29.008 2.03401C29.1537 2.04451 29.2982 2.06726 29.44 2.10201L29.456 2.10801C29.6476 2.20727 29.8019 2.36582 29.896 2.56001L29.916 2.62401C29.932 2.69201 29.95 2.80401 29.966 2.99201C29.998 3.39201 30 3.92601 30 4.80001V9.20001C30 10.074 30 10.608 29.966 11.008C29.9555 11.1537 29.9328 11.2982 29.898 11.44L29.894 11.45L29.892 11.454C29.7962 11.6416 29.6436 11.7942 29.456 11.89H29.452L29.448 11.894L29.44 11.898L29.376 11.916C29.2551 11.9441 29.132 11.9608 29.008 11.966C28.608 11.998 28.074 12 27.2 12H22.8C21.928 12 21.392 12 20.992 11.966C20.8463 11.9555 20.7018 11.9328 20.56 11.898L20.546 11.89C20.3584 11.7942 20.2058 11.6416 20.11 11.454H20.108L20.102 11.44L20.084 11.376C20.056 11.2552 20.0392 11.132 20.034 11.008C20.002 10.608 20 10.074 20 9.20001V4.80001C20 3.92601 20 3.39201 20.034 2.99201C20.0445 2.84636 20.0672 2.70185 20.102 2.56001L20.11 2.54401C20.2058 2.35638 20.3584 2.20379 20.546 2.10801L20.56 2.10201L20.624 2.08401C20.7449 2.05597 20.868 2.03923 20.992 2.03401C21.392 2.00201 21.926 2.00001 22.8 2.00001ZM18.32 19.64C17.994 20.282 17.994 21.122 17.994 22.8V27.2C17.994 28.88 17.994 29.72 18.32 30.36C18.608 30.924 19.066 31.384 19.632 31.672C20.274 32 21.114 32 22.792 32H27.192C28.872 32 29.712 32 30.352 31.672C30.916 31.384 31.376 30.926 31.664 30.36C31.992 29.718 31.992 28.878 31.992 27.2V22.8C31.992 21.12 31.992 20.28 31.664 19.64C31.3763 19.075 30.917 18.6157 30.352 18.328C29.71 18.002 28.87 18.002 27.192 18.002H22.792C21.112 18.002 20.272 18.002 19.632 18.328C19.067 18.6157 18.6077 19.075 18.32 19.64ZM27.2 20.002H22.8C21.928 20.002 21.392 20.002 20.992 20.036C20.8463 20.0465 20.7018 20.0693 20.56 20.104L20.548 20.11H20.546C20.3584 20.2058 20.2058 20.3584 20.11 20.546L20.108 20.55L20.102 20.562L20.084 20.626C20.056 20.7469 20.0392 20.8701 20.034 20.994C20.002 21.394 20 21.928 20 22.802V27.202C20 28.074 20 28.61 20.034 29.01C20.0445 29.1557 20.0672 29.3002 20.102 29.442L20.11 29.456C20.208 29.6488 20.3659 29.8046 20.56 29.9L20.624 29.918C20.692 29.934 20.804 29.952 20.992 29.968C21.392 30 21.926 30.002 22.8 30.002H27.2C28.072 30.002 28.608 30.002 29.008 29.968C29.1537 29.9575 29.2982 29.9348 29.44 29.9L29.448 29.896L29.456 29.892C29.6436 29.7962 29.7962 29.6436 29.892 29.456L29.898 29.442L29.916 29.378C29.944 29.2572 29.9608 29.134 29.966 29.01C29.998 28.61 30 28.076 30 27.202V22.802C30 21.93 30 21.394 29.966 20.994C29.9555 20.8484 29.9328 20.7038 29.898 20.562L29.892 20.546C29.7959 20.3591 29.6433 20.2073 29.456 20.112L29.44 20.104L29.376 20.086C29.2551 20.058 29.132 20.0412 29.008 20.036C28.608 20.004 28.074 20.002 27.2 20.002ZM0 22.8C0 21.12 0 20.28 0.326 19.64C0.614 19.076 1.072 18.616 1.638 18.328C2.28 18.002 3.12 18.002 4.798 18.002H9.198C10.878 18.002 11.718 18.002 12.358 18.328C12.922 18.616 13.382 19.074 13.67 19.64C13.996 20.282 13.996 21.122 13.996 22.8V27.2C13.996 28.88 13.996 29.72 13.67 30.36C13.3823 30.925 12.923 31.3843 12.358 31.672C11.716 32 10.876 32 9.198 32H4.798C3.118 32 2.278 32 1.638 31.672C1.07301 31.3843 0.613706 30.925 0.326 30.36C0 29.718 0 28.878 0 27.2V22.8ZM4.8 20H9.2C10.074 20 10.608 20 11.008 20.034C11.1537 20.0445 11.2982 20.0673 11.44 20.102L11.452 20.108H11.454C11.6471 20.2066 11.8029 20.3652 11.898 20.56L11.916 20.624C11.932 20.692 11.95 20.804 11.966 20.992C11.998 21.392 12 21.926 12 22.8V27.2C12 28.072 12 28.608 11.966 29.008C11.9555 29.1537 11.9328 29.2982 11.898 29.44L11.892 29.454C11.796 29.6427 11.6427 29.7961 11.454 29.892C11.454 29.892 11.446 29.896 11.44 29.898L11.376 29.916C11.2551 29.9441 11.132 29.9608 11.008 29.966C10.608 29.998 10.074 30 9.2 30H4.8C3.926 30 3.392 30 2.992 29.966C2.84635 29.9555 2.70183 29.9328 2.56 29.898L2.546 29.892C2.35837 29.7962 2.20578 29.6436 2.11 29.456L2.102 29.44L2.084 29.376C2.05596 29.2552 2.03922 29.132 2.034 29.008C2.002 28.608 2 28.074 2 27.2V22.8C2 21.928 2 21.392 2.034 20.992C2.04449 20.8464 2.06724 20.7018 2.102 20.56L2.11 20.546C2.20578 20.3584 2.35837 20.2058 2.546 20.11L2.56 20.102L2.624 20.084C2.74485 20.056 2.86805 20.0392 2.992 20.034C3.392 20.002 3.926 20 4.8 20Z"
                            fill="currentColor"
                            />
                        </svg>
                        <p>Katalog</p>
                    </a>
                </div>
            </li>
            <li>
                <div class="navIcons">
                    <a class="bottomBarNavLinks" href="">
                        <svg
                            width="34"
                            height="40"
                            viewBox="0 0 34 40"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                            d="M20.4937 39C43.5 33.3 30.6171 10.5 14.9727 1C13.1318 7.65 10.3704 9.55 4.84938 16.2C-2.46172 25.0046 1.16936 35.2 11.2908 39C9.7567 37.1 5.79997 33.11 8.5294 27.6C9.47059 25.7 11.3529 23.8 10.4118 20C12.2527 20.95 16.0589 21.9 17 26.65C18.5342 24.75 20.1248 20.76 18.6528 16.2C30.1766 24.75 25.4707 33.3 20.4937 39Z"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            />
                        </svg>
                        <p>Brands</p>
                    </a>
                </div>
            </li>
            <li>
                <div class="navIcons">
                    <a class="bottomBarNavLinks" href="">
                        <svg
                            width="37"
                            height="32"
                            viewBox="0 0 37 32"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                            d="M33.142 18.284L21.828 29.6C21.0779 30.3499 20.0606 30.7711 19 30.7711C17.9393 30.7711 16.9221 30.3499 16.172 29.6L4.85798 18.286C3.92272 17.3589 3.17979 16.2562 2.67183 15.0412C2.16388 13.8262 1.90092 12.5228 1.89803 11.2059C1.89515 9.88902 2.15241 8.58452 2.65503 7.3673C3.15766 6.15009 3.89575 5.04415 4.82694 4.11295C5.75813 3.18176 6.86408 2.44367 8.08129 1.94104C9.29851 1.43842 10.603 1.18116 11.9199 1.18404C13.2368 1.18693 14.5402 1.44989 15.7552 1.95784C16.9702 2.4658 18.0729 3.20873 19 4.14399C20.8831 2.30773 23.4139 1.28732 26.044 1.3038C28.6742 1.32028 31.192 2.37232 33.0519 4.23203C34.9119 6.09175 35.9643 8.60937 35.9812 11.2395C35.998 13.8697 34.978 16.4006 33.142 18.284Z"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            />
                        </svg>
                        <p>Wishlist</p>
                    </a>
                </div>
            </li>
        </ul>
    </div>

    <!-- Mobile Navigation -->
    <nav class="mobileNav">
        <div class="logo">
            <a href="">
                <img src="/public/assets/logoCatalog.png" alt="Logo Catalog" width="40px">
            </a>
        </div>
        <div class="searchBar">
            <input type="text" placeholder="Cari product...">
            <a href="#">
                <i class="fa fa-search"></i>
            </a>
        </div>
    </nav>

    <!-- Mobile Bottom Bar NavigationLinks -->
    <!-- Tablet Bottom Bar NavigationLinks -->
     <div class="mobileNavLinks">
        <ul>
            <!-- navLinks with Icons -->
            <li>
                <div class="navIcons">
                    <a class="bottomBarNavLinks bottomBarLinksActive" href="">
                    <svg
                        width="15"
                        height="19"
                        viewBox="0 0 30 34"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        class="text-blue-600"
                    >
                        <path
                        d="M2.26193 31.4446H9.99755V19.1162H19.6692V31.4446H27.4048V12.5874L14.8334 3.07925L2.26193 12.5874V31.4446ZM0.166687 33.5398V11.5398L14.8334 0.460205L29.5 11.5398V33.5398H17.5739V21.2114H12.0928V33.5398H0.166687Z"
                        fill="currentColor"
                        />
                    </svg>
                    <p>Home</p>
                    </a>
                </div>
                </li>
            <li>
                <div class="navIcons">
                    <a class="bottomBarNavLinks" href="">
                        <svg
                            width="17"
                            height="17"
                            viewBox="0 0 32 32"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M0 4.80001C0 3.12001 0 2.28001 0.326 1.64001C0.614 1.07601 1.072 0.616014 1.638 0.328014C2.28 0.00201416 3.12 0.00201416 4.798 0.00201416H9.198C10.878 0.00201416 11.718 0.00201416 12.358 0.328014C12.922 0.616014 13.382 1.07401 13.67 1.64001C13.996 2.28201 13.996 3.12201 13.996 4.80001V9.20001C13.996 10.88 13.996 11.72 13.67 12.36C13.3823 12.925 12.923 13.3843 12.358 13.672C11.716 13.998 10.876 13.998 9.198 13.998H4.798C3.118 13.998 2.278 13.998 1.638 13.672C1.07301 13.3843 0.613706 12.925 0.326 12.36C0 11.718 0 10.878 0 9.20001V4.80001ZM4.8 2.00001H9.2C10.074 2.00001 10.608 2.00001 11.008 2.03401C11.1537 2.04451 11.2982 2.06726 11.44 2.10201L11.454 2.10801C11.6471 2.20657 11.8029 2.3652 11.898 2.56001L11.916 2.62401C11.932 2.69201 11.95 2.80401 11.966 2.99201C11.998 3.39201 12 3.92601 12 4.80001V9.20001C12 10.074 12 10.608 11.966 11.008C11.9555 11.1537 11.9328 11.2982 11.898 11.44L11.892 11.452V11.454C11.7957 11.6419 11.6424 11.7946 11.454 11.89C11.454 11.89 11.446 11.894 11.44 11.898L11.376 11.916C11.2551 11.9441 11.132 11.9608 11.008 11.966C10.608 11.998 10.074 12 9.2 12H4.8C3.926 12 3.392 12 2.992 11.966C2.84635 11.9555 2.70183 11.9328 2.56 11.898L2.546 11.89C2.35837 11.7942 2.20578 11.6416 2.11 11.454L2.1 11.44L2.082 11.376C2.05396 11.2552 2.03722 11.132 2.032 11.008C2.002 10.608 2 10.074 2 9.20001V4.80001C2 3.92601 2 3.39201 2.034 2.99201C2.04449 2.84636 2.06724 2.70185 2.102 2.56001L2.11 2.54401C2.20578 2.35638 2.35837 2.20379 2.546 2.10801L2.56 2.10201L2.624 2.08401C2.74485 2.05597 2.86805 2.03923 2.992 2.03401C3.392 2.00201 3.926 2.00001 4.8 2.00001ZM18 4.80001C18 3.12001 18 2.28001 18.326 1.64001C18.614 1.07601 19.072 0.616014 19.638 0.328014C20.28 0.00201416 21.12 0.00201416 22.798 0.00201416H27.198C28.878 0.00201416 29.718 0.00201416 30.358 0.328014C30.922 0.616014 31.382 1.07401 31.67 1.64001C31.998 2.28201 31.998 3.12201 31.998 4.80001V9.20001C31.998 10.88 31.998 11.72 31.67 12.36C31.3823 12.925 30.923 13.3843 30.358 13.672C29.716 13.998 28.876 13.998 27.198 13.998H22.798C21.118 13.998 20.278 13.998 19.638 13.672C19.073 13.3843 18.6137 12.925 18.326 12.36C18 11.718 18 10.878 18 9.20001V4.80001ZM22.8 2.00001H27.2C28.072 2.00001 28.608 2.00001 29.008 2.03401C29.1537 2.04451 29.2982 2.06726 29.44 2.10201L29.456 2.10801C29.6476 2.20727 29.8019 2.36582 29.896 2.56001L29.916 2.62401C29.932 2.69201 29.95 2.80401 29.966 2.99201C29.998 3.39201 30 3.92601 30 4.80001V9.20001C30 10.074 30 10.608 29.966 11.008C29.9555 11.1537 29.9328 11.2982 29.898 11.44L29.894 11.45L29.892 11.454C29.7962 11.6416 29.6436 11.7942 29.456 11.89H29.452L29.448 11.894L29.44 11.898L29.376 11.916C29.2551 11.9441 29.132 11.9608 29.008 11.966C28.608 11.998 28.074 12 27.2 12H22.8C21.928 12 21.392 12 20.992 11.966C20.8463 11.9555 20.7018 11.9328 20.56 11.898L20.546 11.89C20.3584 11.7942 20.2058 11.6416 20.11 11.454H20.108L20.102 11.44L20.084 11.376C20.056 11.2552 20.0392 11.132 20.034 11.008C20.002 10.608 20 10.074 20 9.20001V4.80001C20 3.92601 20 3.39201 20.034 2.99201C20.0445 2.84636 20.0672 2.70185 20.102 2.56001L20.11 2.54401C20.2058 2.35638 20.3584 2.20379 20.546 2.10801L20.56 2.10201L20.624 2.08401C20.7449 2.05597 20.868 2.03923 20.992 2.03401C21.392 2.00201 21.926 2.00001 22.8 2.00001ZM18.32 19.64C17.994 20.282 17.994 21.122 17.994 22.8V27.2C17.994 28.88 17.994 29.72 18.32 30.36C18.608 30.924 19.066 31.384 19.632 31.672C20.274 32 21.114 32 22.792 32H27.192C28.872 32 29.712 32 30.352 31.672C30.916 31.384 31.376 30.926 31.664 30.36C31.992 29.718 31.992 28.878 31.992 27.2V22.8C31.992 21.12 31.992 20.28 31.664 19.64C31.3763 19.075 30.917 18.6157 30.352 18.328C29.71 18.002 28.87 18.002 27.192 18.002H22.792C21.112 18.002 20.272 18.002 19.632 18.328C19.067 18.6157 18.6077 19.075 18.32 19.64ZM27.2 20.002H22.8C21.928 20.002 21.392 20.002 20.992 20.036C20.8463 20.0465 20.7018 20.0693 20.56 20.104L20.548 20.11H20.546C20.3584 20.2058 20.2058 20.3584 20.11 20.546L20.108 20.55L20.102 20.562L20.084 20.626C20.056 20.7469 20.0392 20.8701 20.034 20.994C20.002 21.394 20 21.928 20 22.802V27.202C20 28.074 20 28.61 20.034 29.01C20.0445 29.1557 20.0672 29.3002 20.102 29.442L20.11 29.456C20.208 29.6488 20.3659 29.8046 20.56 29.9L20.624 29.918C20.692 29.934 20.804 29.952 20.992 29.968C21.392 30 21.926 30.002 22.8 30.002H27.2C28.072 30.002 28.608 30.002 29.008 29.968C29.1537 29.9575 29.2982 29.9348 29.44 29.9L29.448 29.896L29.456 29.892C29.6436 29.7962 29.7962 29.6436 29.892 29.456L29.898 29.442L29.916 29.378C29.944 29.2572 29.9608 29.134 29.966 29.01C29.998 28.61 30 28.076 30 27.202V22.802C30 21.93 30 21.394 29.966 20.994C29.9555 20.8484 29.9328 20.7038 29.898 20.562L29.892 20.546C29.7959 20.3591 29.6433 20.2073 29.456 20.112L29.44 20.104L29.376 20.086C29.2551 20.058 29.132 20.0412 29.008 20.036C28.608 20.004 28.074 20.002 27.2 20.002ZM0 22.8C0 21.12 0 20.28 0.326 19.64C0.614 19.076 1.072 18.616 1.638 18.328C2.28 18.002 3.12 18.002 4.798 18.002H9.198C10.878 18.002 11.718 18.002 12.358 18.328C12.922 18.616 13.382 19.074 13.67 19.64C13.996 20.282 13.996 21.122 13.996 22.8V27.2C13.996 28.88 13.996 29.72 13.67 30.36C13.3823 30.925 12.923 31.3843 12.358 31.672C11.716 32 10.876 32 9.198 32H4.798C3.118 32 2.278 32 1.638 31.672C1.07301 31.3843 0.613706 30.925 0.326 30.36C0 29.718 0 28.878 0 27.2V22.8ZM4.8 20H9.2C10.074 20 10.608 20 11.008 20.034C11.1537 20.0445 11.2982 20.0673 11.44 20.102L11.452 20.108H11.454C11.6471 20.2066 11.8029 20.3652 11.898 20.56L11.916 20.624C11.932 20.692 11.95 20.804 11.966 20.992C11.998 21.392 12 21.926 12 22.8V27.2C12 28.072 12 28.608 11.966 29.008C11.9555 29.1537 11.9328 29.2982 11.898 29.44L11.892 29.454C11.796 29.6427 11.6427 29.7961 11.454 29.892C11.454 29.892 11.446 29.896 11.44 29.898L11.376 29.916C11.2551 29.9441 11.132 29.9608 11.008 29.966C10.608 29.998 10.074 30 9.2 30H4.8C3.926 30 3.392 30 2.992 29.966C2.84635 29.9555 2.70183 29.9328 2.56 29.898L2.546 29.892C2.35837 29.7962 2.20578 29.6436 2.11 29.456L2.102 29.44L2.084 29.376C2.05596 29.2552 2.03922 29.132 2.034 29.008C2.002 28.608 2 28.074 2 27.2V22.8C2 21.928 2 21.392 2.034 20.992C2.04449 20.8464 2.06724 20.7018 2.102 20.56L2.11 20.546C2.20578 20.3584 2.35837 20.2058 2.546 20.11L2.56 20.102L2.624 20.084C2.74485 20.056 2.86805 20.0392 2.992 20.034C3.392 20.002 3.926 20 4.8 20Z"
                            fill="currentColor"
                            />
                        </svg>
                        <p>Katalog</p>
                    </a>
                </div>
            </li>
            <li>
                <div class="navIcons">
                    <a class="bottomBarNavLinks" href="">
                        <svg
                            width="19"
                            height="25"
                            viewBox="0 0 34 40"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                            d="M20.4937 39C43.5 33.3 30.6171 10.5 14.9727 1C13.1318 7.65 10.3704 9.55 4.84938 16.2C-2.46172 25.0046 1.16936 35.2 11.2908 39C9.7567 37.1 5.79997 33.11 8.5294 27.6C9.47059 25.7 11.3529 23.8 10.4118 20C12.2527 20.95 16.0589 21.9 17 26.65C18.5342 24.75 20.1248 20.76 18.6528 16.2C30.1766 24.75 25.4707 33.3 20.4937 39Z"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            />
                        </svg>
                        <p>Brands</p>
                    </a>
                </div>
            </li>
            <li>
                <div class="navIcons">
                    <a class="bottomBarNavLinks" href="">
                        <svg
                            width="22"
                            height="27"
                            viewBox="0 0 37 32"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                            d="M33.142 18.284L21.828 29.6C21.0779 30.3499 20.0606 30.7711 19 30.7711C17.9393 30.7711 16.9221 30.3499 16.172 29.6L4.85798 18.286C3.92272 17.3589 3.17979 16.2562 2.67183 15.0412C2.16388 13.8262 1.90092 12.5228 1.89803 11.2059C1.89515 9.88902 2.15241 8.58452 2.65503 7.3673C3.15766 6.15009 3.89575 5.04415 4.82694 4.11295C5.75813 3.18176 6.86408 2.44367 8.08129 1.94104C9.29851 1.43842 10.603 1.18116 11.9199 1.18404C13.2368 1.18693 14.5402 1.44989 15.7552 1.95784C16.9702 2.4658 18.0729 3.20873 19 4.14399C20.8831 2.30773 23.4139 1.28732 26.044 1.3038C28.6742 1.32028 31.192 2.37232 33.0519 4.23203C34.9119 6.09175 35.9643 8.60937 35.9812 11.2395C35.998 13.8697 34.978 16.4006 33.142 18.284Z"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            />
                        </svg>
                        <p>Wishlist</p>
                    </a>
                </div>
            </li>
        </ul>
    </div>

    <!-- Desktop Footer -->
    <footer>
        <div class="footerTop">
            <div class="footerInfo">
                <a href="">
                    <img src="/public/assets/logoCatalog.png" alt="Logo Catalog" width="40px">
                </a>
                <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Eveniet expedita aliquid, quod dolore libero sint assumenda fugiat qui eligendi quam vel vitae, mollitia itaque, perferendis recusandae inventore cumque totam accusamus?</p>
            </div>
            <div class="footerLinks">
                <div class="quickLinks">
                    <h1>Quick Links</h1>
                    <ul>
                        <li><a href="">Home</a></li>
                        <li><a href="">Katalog</a></li>
                        <li><a href="">Brands</a></li>
                        <li><a href="">Wishlist</a></li>
                    </ul>
                </div>
                <div class="topKategori">
                    <h1>Top Kategori</h1>
                    <ul>
                        <li><a href="">Kategori 1</a></li>
                        <li><a href="">Kategori 2</a></li>
                        <li><a href="">Kategori 3</a></li>
                        <li><a href="">Kategori 4</a></li>
                    </ul>
                </div>
            </div>
            <div class="footerSocial">
                <div class="socialLinks">
                    <h1>Contact Information</h1>
                    <ul>
                        <li>
                            <div class="contactIcons">
                                <a href="">
                                    <svg 
                                        width="27"
                                        height="26"
                                        viewBox="0 0 27 26"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                        d="M18.1719 17.5213L18.7799 16.9173L17.3679 15.4987L16.7626 16.1027L18.1719 17.5213ZM20.8186 16.664L23.3666 18.0493L24.3199 16.292L21.7732 14.908L20.8186 16.664ZM23.8559 20.8013L21.9626 22.6853L23.3719 24.1027L25.2652 22.22L23.8559 20.8013ZM20.8079 23.2907C18.8746 23.472 13.8746 23.3107 8.45857 17.9267L7.04791 19.344C12.9572 25.22 18.5826 25.508 20.9946 25.2827L20.8079 23.2907ZM8.45857 17.9267C3.29724 12.7933 2.44124 8.47732 2.33457 6.60399L0.337239 6.71732C0.470572 9.07465 1.53057 13.8587 7.04791 19.344L8.45857 17.9267ZM10.2919 9.68665L10.6746 9.30532L9.26657 7.88799L8.88391 8.26799L10.2919 9.68665ZM10.9786 4.45865L9.29857 2.21332L7.69724 3.41332L9.37724 5.65732L10.9786 4.45865ZM3.64391 1.72399L1.55057 3.80399L2.96124 5.22265L5.05324 3.14265L3.64391 1.72399ZM9.58791 8.97732C8.88124 8.26799 8.88124 8.26799 8.88124 8.27065H8.87857L8.87457 8.27599C8.81121 8.33987 8.75448 8.41001 8.70524 8.48532C8.63324 8.59199 8.55457 8.73199 8.48791 8.90932C8.32561 9.36709 8.28517 9.8592 8.37057 10.3373C8.54924 11.4907 9.34391 13.0147 11.3786 15.0387L12.7892 13.62C10.8839 11.7267 10.4306 10.5747 10.3466 10.0307C10.3066 9.77199 10.3479 9.64399 10.3599 9.61465C10.3679 9.59688 10.3679 9.59421 10.3599 9.60665C10.348 9.62505 10.3346 9.64245 10.3199 9.65865L10.3066 9.67199L10.2932 9.68399L9.58791 8.97732ZM11.3786 15.0387C13.4146 17.0627 14.9466 17.852 16.1012 18.028C16.6919 18.1187 17.1679 18.0467 17.5292 17.912C17.7316 17.838 17.9207 17.7317 18.0892 17.5973L18.1559 17.5373L18.1652 17.5293L18.1692 17.5253L18.1706 17.5227C18.1706 17.5227 18.1719 17.5213 17.4666 16.812C16.7599 16.1027 16.7639 16.1013 16.7639 16.1013L16.7666 16.0987L16.7692 16.096L16.7772 16.0893L16.7906 16.076L16.8412 16.036C16.8537 16.028 16.8506 16.0289 16.8319 16.0387C16.7986 16.0507 16.6679 16.092 16.4052 16.052C15.8532 15.9667 14.6932 15.5133 12.7892 13.62L11.3786 15.0387ZM9.29857 2.21199C7.93857 0.398654 5.26657 0.110654 3.64391 1.72399L5.05324 3.14265C5.76257 2.43732 7.02124 2.51065 7.69724 3.41332L9.29857 2.21199ZM2.33591 6.60532C2.30924 6.14399 2.52124 5.66132 2.96124 5.22399L1.54924 3.80532C0.833239 4.51732 0.269239 5.52532 0.337239 6.71732L2.33591 6.60532ZM21.9626 22.6853C21.5972 23.0507 21.2026 23.256 20.8092 23.292L20.9946 25.2827C21.9746 25.1907 22.7759 24.6973 23.3732 24.104L21.9626 22.6853ZM10.6746 9.30532C11.9879 7.99999 12.0852 5.93732 10.9799 4.45999L9.37857 5.65865C9.91591 6.37732 9.83591 7.31999 9.26524 7.88932L10.6746 9.30532ZM23.3679 18.0507C24.4572 18.6427 24.6266 20.0373 23.8572 20.8027L25.2679 22.22C27.0546 20.4427 26.5039 17.4787 24.3212 16.2933L23.3679 18.0507ZM18.7799 16.9187C19.2919 16.4093 20.1159 16.284 20.8199 16.6653L21.7746 14.9093C20.3292 14.1227 18.5372 14.34 17.3692 15.5L18.7799 16.9187Z"
                                        fill="#EDA412"/>
                                    </svg>
                                    <span>+62 812 3456 7890</span>
                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="contactIcons">
                                <a href="">
                                    <svg 
                                        width="26"
                                        height="21"
                                        viewBox="0 0 26 21"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path 
                                            d="M4.66675 0.666687H22.0001C23.0609 0.666687 24.0784 1.08811 24.8285 1.83826C25.5787 2.58841 26.0001 3.60582 26.0001 4.66669V16.6667C26.0001 17.7276 25.5787 18.745 24.8285 19.4951C24.0784 20.2453 23.0609 20.6667 22.0001 20.6667H4.66675C3.60588 20.6667 2.58847 20.2453 1.83832 19.4951C1.08818 18.745 0.666748 17.7276 0.666748 16.6667V4.66669C0.666748 3.60582 1.08818 2.58841 1.83832 1.83826C2.58847 1.08811 3.60588 0.666687 4.66675 0.666687ZM4.66675 2.00002C4.00008 2.00002 3.41341 2.22669 2.96008 2.62669L13.3334 9.33335L23.7067 2.62669C23.2534 2.22669 22.6667 2.00002 22.0001 2.00002H4.66675ZM13.3334 10.9467L2.17341 3.70669C2.06675 4.00002 2.00008 4.33335 2.00008 4.66669V16.6667C2.00008 17.3739 2.28103 18.0522 2.78113 18.5523C3.28123 19.0524 3.9595 19.3334 4.66675 19.3334H22.0001C22.7073 19.3334 23.3856 19.0524 23.8857 18.5523C24.3858 18.0522 24.6667 17.3739 24.6667 16.6667V4.66669C24.6667 4.33335 24.6001 4.00002 24.4934 3.70669L13.3334 10.9467Z"
                                            fill="#EDA412"
                                        />
                                    </svg>
                                    <span><EMAIL></span>
                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="contactIcons">
                                <a href="">
                                    <img src="/public/assets/facebook.png" alt="">
                                    <span>Facebook</span>
                                </a>
                            </div>
                        </li>
                        <li>
                            <div class="contactIcons">
                                <a href="">
                                    <img src="/public/assets/instagram.png" alt="">
                                    <span>Instagram</span>
                                </a>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="companyAddress">
                    <h1>Company Address</h1>
                    <div class="addressMaps">
                        <a href="">
                            <img src="/public/assets/gmaps.png" alt="">
                        </a>
                        <p>Jl. Raya Cibadak No.123, Cibadak, Cimahi, Jawa Barat</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="footerBottom">
            <p>© 2025 GG Store Catalogs. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>